<?php

namespace App\Livewire\Dashboard\Widgets;

use App\Traits\LivewireGeneralFunctions;
use Livewire\Component;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;

class BiorhythmFilter extends Component
{
    use LivewireGeneralFunctions;

    public $lta_days = [];
    public $selectedLongDay = null;
    public $widget;
    public $user;
    public $ran_ana = 0;

    public function mount($widget = null)
    {
        $this->widget = $widget;
        $this->user = getUserDetails();
        $this->selectedLongDay = request()->query('longDay') ?? null;
        $this->lta_days = DB::table('lta_days')->orderBy('days', 'ASC')->get(['id','days']);
        
        // Check if this is a random analysis
        $this->ran_ana = request()->has('days') ? 1 : 0;
    }

    public function updatedSelectedLongDay($value)
    {
        // Dispatch event to notify other components about day selection
        $this->dispatch('daySelected', $value);
    }

    public function render()
    {
        return view('livewire.dashboard.widgets.biorhythm-filter');
    }
}
