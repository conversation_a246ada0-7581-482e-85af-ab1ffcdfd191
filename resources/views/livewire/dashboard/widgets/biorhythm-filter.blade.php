<div class="biorhythm-filter-container">
    <div class="filter-row">
        <!-- Long Term Filter Dropdown -->
        <div class="filter-item">
            <select class="custom-select border-none mt-2 mt-sm-0" id="longday" onchange="redirectWithParam(this)">
                <option value="">Tage auswählen</option>
                @foreach($lta_days as $days)
                    <option value="{{ $days->days }}" @if(request()->get('longDay') == $days->days) selected @endif>
                        {{ $days->days }} Tage
                    </option>
                @endforeach
            </select>
        </div>

        <!-- Toggle Section (M/J/B/L) -->
        <div class="filter-item">
            <div class="fourX-toggle-section">
                <div class="toggle-container">
                    <input type="radio" value="1" name="mycb" id="checkbox1">
                    <label for="checkbox1">
                        <span class="checklistBox mycb @if($user->calculation_with == 1 && $ran_ana != 1) round @endif" 
                              id="cb-my1" 
                              data-href="@if(request()->proid) {{ route('dashboard.product', [request()->proid, request()->subid]) }} @else {{ route('dashboard.ownProduct', [request()->ownid, request()->ownsubid]) }} @endif"></span>
                    </label>

                    <input type="radio" value="0" name="mycb" id="checkbox2">
                    <label for="checkbox2">
                        <span class="checklistBox mycb @if($user->calculation_with == 0 && $ran_ana != 1) round @endif" 
                              id="cb-my0" 
                              data-href="@if(request()->proid) {{ route('dashboard.product', [request()->proid, request()->subid]) }} @else {{ route('dashboard.ownProduct', [request()->ownid, request()->ownsubid]) }} @endif"></span>
                    </label>

                    <input type="radio" value="1" name="rlcb" id="checkbox3">
                    <label for="checkbox3">
                        @if(request()->proid && request()->days)
                            <span class="checklistBox rlcb @if($ran_ana) round @endif" 
                                  id="cb-rl1" 
                                  data-href="{{ (request()->comid == null) ? route('dashboard.product', [request()->proid, request()->subid]) : route('dashboard.combo.product', [request()->proid, request()->subid, request()->comid]) }}"></span>
                        @elseif(request()->ownid && request()->days)
                            <span class="checklistBox rlcb @if($ran_ana) round @endif" 
                                  id="cb-rl1" 
                                  data-href="{{ (request()->comid == null) ? route('dashboard.ownProduct', [request()->ownid, request()->ownsubid]) : route('dashboard.combo.ownProduct', [request()->ownid, request()->ownsubid, request()->comid]) }}"></span>
                        @elseif(request()->proid)
                            <span class="checklistBox rlcb @if($ran_ana) round @endif" 
                                  id="cb-rl1" 
                                  data-href="{{ route('dashboard.product', [request()->proid, request()->subid]) }}"></span>
                        @else
                            <span class="checklistBox rlcb @if($ran_ana) round @endif" 
                                  id="cb-rl1" 
                                  data-href="{{ route('dashboard.ownProduct', [request()->ownid, request()->ownsubid]) }}"></span>
                        @endif
                    </label>

                    @if(Gate::allows('checkAccess','checkLTA'))
                        <input type="radio" value="2" name="rlcb" id="checkbox4">
                        <label for="checkbox4">
                            @if(request()->proid && request()->days)
                                <span class="checklistBox rlcb round" 
                                      id="cb-rl2" 
                                      data-href="{{ (request()->comid == null) ? route('dashboard.product', [request()->proid, request()->subid]) : route('dashboard.combo.product', [request()->proid, request()->subid, request()->comid]) }}"></span>
                            @elseif(request()->ownid && request()->days)
                                <span class="checklistBox rlcb round" 
                                      id="cb-rl2" 
                                      data-href="{{ (request()->comid == null) ? route('dashboard.ownProduct', [request()->ownid, request()->ownsubid]) : route('dashboard.combo.ownProduct', [request()->ownid, request()->ownsubid, request()->comid]) }}"></span>
                            @else
                                <span class="checklistBox rlcb" id="cb-rl2" data-href=""></span>
                            @endif
                        </label>
                    @endif
                </div>
                <div class="checkbox-names">
                    <span data-toggle="tooltip" data-placement="bottom" title="Monat" data-original-title="Monat">M</span>
                    <span data-toggle="tooltip" data-placement="bottom" title="Jahr" data-original-title="Jahr">J</span>
                    <span data-toggle="tooltip" data-placement="bottom" title="Beobachtereffekt (Momentenanalyse)" data-original-title="Beobachtereffekt (Momentenanalyse)">B</span>
                    @if(Gate::allows('checkAccess','checkLTA'))
                        <span data-toggle="tooltip" data-placement="bottom" title="Langzeitanalyse" data-original-title="Langzeitanalyse">L</span>
                    @endif
                </div>
            </div>
        </div>

        <!-- Calendar Icon -->
        <div class="filter-item">
            <i class="fas fa-calendar-alt f-22 text-success" 
               id="open-changeDate" 
               data-toggle="tooltip" 
               data-placement="bottom" 
               data-title="{{ date_change($user->datumcore) }}" 
               data-original-title="" 
               title=""></i>
        </div>
    </div>

    <!-- Change Date Modal -->
    <div class="modal fade mt-5" id="changeDate" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Datum System Dashboard</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <input type="text" id="datePicker" class="form-control datef" placeholder="mm/dd/yyyy" value="{{ date_change($user->datumcore) }}" style="width:100%">
                        </div>
                    </div>
                </div>
                <div class="modal-footer modalFooter_padding">
                    <div class="button-link mt-3 mt-sm-0">
                        <button type="button" class="btn btn-warning" onclick="changeDatumcore('Reset')">Reset</button>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Schließen</button>
                        <button type="button" class="btn btn-success" onclick="changeDatumcore('Save')">Speichern</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.biorhythm-filter-container {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.filter-row {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-item {
    display: flex;
    align-items: center;
}

.filter-item select {
    min-width: 150px;
}

.fourX-toggle-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.toggle-container {
    display: flex;
    gap: 5px;
}

.checkbox-names {
    display: flex;
    gap: 15px;
    font-size: 12px;
    font-weight: bold;
}

.checkbox-names span {
    text-align: center;
    min-width: 20px;
}

/* Looping opacity animation */
@keyframes pulseOpacity {
    0%   { opacity: 1; }
    50%  { opacity: 0.4; }
    100% { opacity: 1; }
}

.opacity-pulse {
    animation: pulseOpacity 1.5s ease-in-out infinite;
    pointer-events: none;
    cursor: not-allowed;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .filter-row {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .filter-item {
        justify-content: center;
    }

    .filter-item select {
        width: 100%;
        min-width: auto;
    }
}
</style>

@once
<script>
function redirectWithParam(selectElement) {
    const value = selectElement.value;
    const currentParams = new URLSearchParams(window.location.search);
    const currentLongDay = currentParams.get('longDay');

    if (currentLongDay !== value) {
        // Add pulsing opacity animation and disable
        selectElement.classList.add('opacity-pulse');
        selectElement.disabled = true;

        const dashboardUrl = "{{ request()->attributes->get(\App\Enums\RequestAttributes::DASHBOARD_URL->value) }}";
        const url = new URL(dashboardUrl);

        if (value) {
            url.searchParams.set('longDay', value);
        } else {
            url.searchParams.delete('longDay');
        }

        // Optional small delay to let animation start before redirect
        setTimeout(() => {
            window.location.href = url.toString();
        }, 100);
    }
}

// Change Datumcore function
function changeDatumcore(check) {
    var date = $('#datePicker').val();
    $('#changeDate').modal('hide');

    $.ajax({
        url: "{{ route('Sajax.changeDatumcore') }}",
        type: 'POST',
        data: {
            date: date,
            check: check,
            _token: "{{ csrf_token() }}"
        },
        dataType: "json",
        success: function(msg) {
            if (msg.success == true) {
                toastr.success(msg.message);
                window.location.reload();
            }
        },
        error: function() {
            toastr.error('Error updating date');
        }
    });
}

$(document).ready(function() {
    // Calendar modal functionality
    $("#open-changeDate").on('click', function(){
        $("#changeDate").modal('show');
    });

    // Toggle functionality for M/J/B/L checkboxes
    $("input[name=rlcb]").on('click', function(){
        let value = $(this).val();
        let link = $(`#cb-rl${value}`).data('href');
        if(link) {
            window.location.href = link;
        }
    });

    $("input[name=mycb]").on('click', function(){
        let value = $(this).val();
        let link = $(`#cb-my${value}`).data('href');
        if(link) {
            window.location.href = link;
        }
    });
});
</script>
@endonce
