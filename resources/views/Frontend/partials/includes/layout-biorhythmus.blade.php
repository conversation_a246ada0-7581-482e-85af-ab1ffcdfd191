<style>
    .biorhythm-won-container {
        background-color: #ffffff;
        padding: 15px !important;
        border-radius: 5px;
    }
    .user-personal-info {
        gap: 10px;
        cursor: default;
    }

    .user-profile-pic img {
        min-width: 80px;
        max-width: 80px;
        min-height: 80px;
        max-height: 80px;
        border-radius: 50%;
        object-fit: cover;
    }

    .user-info-content h6, .user-info-content p{
        margin-bottom: 5px;
    }

    button.profile-edit-btn {
        position: absolute;
        top: 0;
        right: 0;
        opacity: 0;
        visibility: hidden;
        transition: all 0.5s ease-in-out;
    }

    .user-personal-info:hover button.profile-edit-btn{
        opacity: 1;
        visibility: visible;
    }

    /* custom preloader */
    .cus-preloader {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 9999;
        background-color: #969696;
        animation: movebg 30s infinite;
        opacity: 0.8;
        background-size: auto;
        display: -webkit-box;
        display: flex;
        -webkit-box-pack: center;
        justify-content: center;
        align-items: center;
    }

    .cus-preloader img {
        height: 70px;
        width: 70px;
        animation: rotating 5s linear infinite;
        position: relative;
        top: 0;
    }

    #dashboardChart{
        display: none;
    }

    .flatpickr-calendar.open{
        z-index: 11111 !important;
    }

    #suggestionList {
        display: none;
    }

    #biorhythmus-header{
        padding: 10px 20px;
        border-bottom: solid 1px #cdcdcd;
    }

    .biorhythm-filter-section {
        padding: 10px 20px;
        border-bottom: solid 1px #cdcdcd;
        background-color: #f8f9fa;
    }

    @media only screen and (max-width: 576px){
        div#biorhythmus-header span {
            display: none;
        }

        button.profile-edit-btn {
            display: none;
        }

        #biorhythmus-header .profile-edit-btn {
            display: block;
            opacity: 1;
            visibility: visible;
            position: relative;
            min-width: fit-content;
        }

        .wran-for-mobile{
            display: block !important;
        }

        .user-personal-info {
            gap: 20px;
            padding-right: 0px;
            margin-bottom: 24px;
        }
    }

</style>
@php
    $__currentUser = getUserDetails();
@endphp
<div class="col-lg-12 mb-3 dashboard-card p-0 @if(!$diagramType) biorhythm-won-container @endif" style="margin-bottom:8px !important;">
    <div class="d-flex justify-content-between align-items-center" id="biorhythmus-header">
        <h5 class="card-title">Ihr Biorhythmus</h5>

        <span>{{ trans('action.setting_for_view').' '. $__currentUser->biorhythmus .' '.trans('action.cron_days') }}</span>

        <button class="btn btn-sm btn-success profile-edit-btn" data-toggle="modal" data-target="#profile-edit-modal"><i class="fas fa-edit"></i></button>
    </div>

    <!-- Biorhythm Filter Component -->
    @if(isset($diagramType) && $diagramType === 'biorythm')
        <div class="biorhythm-filter-section">
            <livewire:dashboard.widgets.biorhythm-filter :widget="$widget ?? []" />
        </div>
    @endif
    <div class="row mx-0">
        <div class="col-lg-4 user-personal-info d-flex align-items-center pl-0">
            <div class="user-profile-pic">
                {{-- <img data-profileimage="true" data-name="{{ $__currentUser->first_name." ".$__currentUser->last_name }}" lazy="loading" data-email="{{ $__currentUser->email }}" data-view="true" src="{{($__currentUser->gender == 2) ? asset("/images/female.jpg") : asset("/images/avatar.png")}}" alt="Profile Image"> --}}
                <img data-profileimage="true" data-name="{{ $__currentUser->first_name." ".$__currentUser->last_name }}" lazy="loading" data-email="{{ $__currentUser->email }}" data-view="true" src="{{($__currentUser->gender == 2) ? asset("default-profile/user-female.svg") : asset("default-profile/user-male.svg")}}" alt="Profile Image">
            </div>
            <div class="user-info-content w-100 position-relative">
                <h6 class="f-16"><span>{{ $__currentUser->first_name }}</span>&nbsp;<span>{{ $__currentUser->last_name }}</span></h6>
                <p><span>{{ $__currentUser->gebdatum }}</span></p>
                <p class="m-0"><span>{{ $__currentUser->cron_email }}({{ trans('action.cron_email') }})</span></p>
                <p class="m-0"><span>{{ $__currentUser->gebort }}</span></p>

                <button class="btn btn-sm btn-success profile-edit-btn" data-toggle="modal" data-target="#profile-edit-modal"><i class="fas fa-edit"></i>&nbsp;{{ trans('action.edit') }}</button>
            </div>
        </div>
        <div class="col-lg-8 user-personal-analytics">
            <div class="cus-preloader"> <img src="{!! asset('images/Fill-4.png') !!}" alt=""></div>
            <div class="chart-canvas-wrapper">
                <canvas id="dashboardChart" height="100"></canvas>
            </div>
        </div>
        <div class="col-lg-12">
            <span class="d-none w-100 text-center wran-for-mobile mt-2">{{ trans('action.setting_for_view').' '. $__currentUser->biorhythmus .' '.trans('action.cron_days') }}</span>
        </div>
    </div>
</div>
<!-- profile edit modal -->
<div class="modal fade" id="profile-edit-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" style="display:inline-block">
                    <i class="fas fa-edit"></i>&nbsp;</i>Edit
                </h5>
                <button type="button" class="close text-danger" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="container">
                    <div class="from-section row">
                        <div class="form-group col-md-6 col-sm-12">
                            <label for="mb-2">{{__('action.first_name')}}</label>
                            <input type="text" id="__first_name" class="form-control" value="{{ $__currentUser->first_name }}">
                        </div>
                        <div class="form-group col-md-6 col-sm-12">
                            <label for="mb-2">{{__('action.last_name')}}</label>
                            <input type="text" id="__last_name" class="form-control" value="{{ $__currentUser->last_name }}">
                        </div>
                        <div class="form-group col-md-6 col-sm-12">
                            <label for="mb-2">{{__('action.birthday_1')}}</label>
                            <input type="text" id="datePicker" class="form-control __datePicker" value="{{ date_change($__currentUser->gebdatum) }}">
                        </div>
                        <div class="form-group col-md-6 col-sm-12">
                            <label for="mb-2" >{{__('action.cron_email')}}</label>
                            <input type="text" id="__cron_email" class="form-control" value="{{ $__currentUser->cron_email }}">
                        </div>
                        <div class="form-group col-md-6 col-sm-12">
                            <label for="mb-2" >{{__('action.placeholder_address')}}</label>
                            <input type="text" id="__gebort" class="form-control" value="{{ $__currentUser->gebort }}">
                        </div>
                        <div class="form-group col-md-6 col-sm-12">
                            <label for="mb-2" >{{ trans('action.lta_choose_days') }}</label></br>
                            <input type="number" name="biorhythmus" value="{{ $__currentUser->biorhythmus }}" id="__biorhythmus" class="form-control" placeholder="{{ trans('action.Min_Value').' 0 '.trans('action.Max_Value').' 180' }}">
                            <ul id="suggestionList" class="list-group"> </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer modalFooter_padding">
                <button type="button" class="btn btn-warning" data-dismiss="modal">{{__('action.close')}}</button>
                <button type="button" class="btn btn-success" data-url="{{ URL::to('/') }}" onclick="current_user_update($(this).data('url'),$('#__first_name').val(),$('#__last_name').val(),$('.__datePicker').val(),$('#__cron_email').val(),$('#__gebort').val(),$('#__biorhythmus').val())">{{__('action.save')}}</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js@3.8.2/dist/chart.min.js"></script>
<script>
    $(document).ready(function() {
        // chart
        let chartYData = [];
        let chartXData = [];
        let chartDateMonth = [];

        $.ajax({
            url: window.location.origin+'/calc/biorhythmus',
            type: 'GET',
            data: {
                days: $('#__biorhythmus').val()??7
            },
            dataType:"json",
            success: function(msg) {
                if(msg.success == true){
                    $.each(msg.lists, function(key, resp) {
                        chartYData.push(resp.value)
                        chartXData.push(resp.day)
                        chartDateMonth.push(resp.dayMonth)
                    })
                    createNewChart('dashboardChart','line', chartDateMonth[0]+'-'+chartDateMonth[chartDateMonth.length - 1], chartYData, "rgb(75, 192, 192)", 2, "rgb(75, 192, 192)", chartXData);
                }
                $('.user-personal-analytics .cus-preloader').hide()

            }
        });
        // rgb(75, 192, 192)


        // Create new chart
        function createNewChart(selector, chartType, chartCaption, chartValue, chartBg, chartBorderWidth, chartBorderColor, chartLabels) {
            const ctx = document.getElementById(`${selector}`).getContext('2d');
            let delayed;

            const chartData = {
                labels: chartLabels,
                datasets: [{
                    label: chartCaption,
                    hoverOffset: 4,
                    data: chartValue,
                    borderWidth: chartBorderWidth,
                    borderColor: chartBorderColor,
                    pointRadius: 3,
                    pointHoverRadius: 5,
                    pointBackgroundColor: "rgb(75, 192, 192)",
                    backgroundColor: chartBg,
                    fill: false,
                    tension: 0.4,
                }]
            };

            const chartConfig = {
                type: chartType,
                data: chartData,
                options: {
                    scales: {
                        x: {
                            ticks: {
                                display: false
                            }
                        }
                    },responsive: true,
                    maintainAspectRatio: false,
                    // animation: {
                    //     tension: {
                    //         duration: 1000,
                    //         easing: 'linear',
                    //         from: 1,
                    //         to: 0,
                    //         loop: false
                    //     }
                    // }
                },plugins:[{
                    afterDatasetsDraw: function(chart, options) {
                        var ctx = chart.ctx;
                        ctx.fillStyle = "#666666";
                        ctx.textAlign = 'center';
                        chart.data.datasets.forEach(function(dataset, i) {
                            var meta = chart.getDatasetMeta(i);
                            meta.data.forEach(function(bar, index) {
                                if(index == 3){
                                    ctx.fillStyle = 'rgba(255, 99, 132, 0.5)';
                                    ctx.fillText(Math.round(dataset.data[index]), bar.x, bar.y - 5);
                                    ctx.fillRect(bar.x - (bar.width / 2), bar.y, bar.width, chart.chartArea.bottom - bar.y);
                                }
                            });
                        });



                    }
                }]
            };

            // Create the chart instance
            const chart = new Chart(ctx, chartConfig);
        }

        const $suggestionList = $('#suggestionList');

        $('#__biorhythmus').on('click change input', function() {
            const searchValue = this.value;
            var numericValue = searchValue.replace(/\D/g, ''); // Remove non-numeric characters
            var intValue = parseInt(numericValue, 10); // Convert to integer

            // Check if the value is within the range of 0 to 180
            if (intValue < 0 || intValue > 365) {
                this.value = '';
                return toastr.warning("{{ trans('action.Min_Value').' 0 '.trans('action.Max_Value').' 365' }}");
            } else {
                this.value = intValue;
            }
            $suggestionList.empty();

            // Add new suggestions based on search value
            if (searchValue.length >= 0) {
                [7, 30, 60, 90, 180, 365].forEach(function(suggestion) {
                    const listItem = $('<li class="list-group-item"></li>').text(suggestion);
                    listItem.on('click', function() {
                        $('#__biorhythmus').val(suggestion);
                        $suggestionList.hide();
                    });
                    $suggestionList.append(listItem);
                });

                $suggestionList.show();
            } else {
                $suggestionList.hide();
            }
        });
    })
</script>
